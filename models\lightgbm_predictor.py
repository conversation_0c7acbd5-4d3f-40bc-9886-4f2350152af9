import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error
import joblib
import os

class LightGBMPredictor:
    def __init__(self):
        self.model = None
        self.feature_columns = None
        self.target_column = None
        
    def prepare_features(self, records):
        """准备特征数据"""
        df = pd.DataFrame(records)
        
        # 基础特征
        features = pd.DataFrame()
        features['sum'] = df['sum'].astype(float)
        features['oddEven'] = (df['oddEven'] == '单').astype(int)
        
        # 历史特征
        for i in range(1, 6):  # 使用前5期数据作为特征
            if i < len(df):
                features[f'sum_lag_{i}'] = df['sum'].shift(i).fillna(0)
                features[f'oddEven_lag_{i}'] = (df['oddEven'].shift(i) == '单').fillna(0).astype(int)
        
        # 统计特征
        features['sum_mean_5'] = features['sum'].rolling(window=5, min_periods=1).mean()
        features['sum_std_5'] = features['sum'].rolling(window=5, min_periods=1).std()
        
        # 目标变量
        target = features['sum'].shift(-1).fillna(0)
        
        return features, target
    
    def train(self, records):
        """训练模型"""
        features, target = self.prepare_features(records)
        
        # 移除最后一行（因为目标变量未知）
        X = features[:-1]
        y = target[:-1]
        
        # 划分训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 设置模型参数
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }
        
        # 创建数据集
        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
        
        # 训练模型
        self.model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=1000,
            early_stopping_rounds=50,
            verbose_eval=False
        )
        
        # 保存特征列名
        self.feature_columns = X.columns.tolist()
        
        return self.model
    
    def predict(self, records):
        """预测下一期结果"""
        if self.model is None:
            raise ValueError("模型尚未训练")
            
        features, _ = self.prepare_features(records)
        latest_features = features.iloc[-1:][self.feature_columns]
        
        # 预测
        prediction = self.model.predict(latest_features)
        
        return {
            'predicted_sum': float(prediction[0]),
            'is_big': prediction[0] > 10,
            'is_odd': int(prediction[0]) % 2 == 1
        }
    
    def save_model(self, path='models/lightgbm_model.txt'):
        """保存模型"""
        if self.model is None:
            raise ValueError("没有可保存的模型")
            
        os.makedirs(os.path.dirname(path), exist_ok=True)
        self.model.save_model(path)
        
    def load_model(self, path='models/lightgbm_model.txt'):
        """加载模型"""
        if not os.path.exists(path):
            raise FileNotFoundError(f"模型文件不存在: {path}")
            
        self.model = lgb.Booster(model_file=path) 