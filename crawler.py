import requests
from bs4 import BeautifulSoup
import time
import json
import urllib3
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import os
import tkinter as tk
from tkinter import ttk
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, GradientBoostingRegressor
from sklearn.svm import SVC
from sklearn.model_selection import GridSearchCV
import threading
from concurrent.futures import ThreadPoolExecutor
from sklearn.preprocessing import StandardScaler
import pandas as pd
from sklearn.linear_model import LogisticRegression
import xgboost as xgb
from sklearn.naive_bayes import GaussianNB
from sklearn.ensemble import VotingClassifier
from sklearn.ensemble import RandomForestRegressor
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
warnings.filterwarnings('ignore')
from xgboost import XGBClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.neural_network import MLPClassifier
import catboost
from catboost import CatBoostClassifier
from sklearn.ensemble import StackingClassifier

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class LotteryUI(tk.Tk):
    @staticmethod
    def to_serializable(val):
        if isinstance(val, (np.floating, np.float32, np.float64)):
            return float(val)
        if isinstance(val, (np.integer, np.int32, np.int64)):
            return int(val)
        if isinstance(val, dict):
            return {k: LotteryUI.to_serializable(v) for k, v in val.items()}
        if isinstance(val, list):
            return [LotteryUI.to_serializable(v) for v in val]
        return val

    def __init__(self):
        super().__init__()
        self.title("彩票数据展示")
        self.latest_records = []
        self.is_fetching = False
        self.prediction_history = []
        self.model_performance = {
            'accuracy': [],
            'precision': [],
            'recall': [],
            'f1': []
        }
        self.setup_ui()
        self.crawler = LotteryCrawler()

    def setup_ui(self):
        self.frame = ttk.Frame(self, padding="10")
        self.frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加状态标签
        self.status_label = ttk.Label(self.frame, text="就绪")
        self.status_label.grid(row=0, column=0, pady=5)
        
        self.get_data_button = ttk.Button(self.frame, text="获取数据", command=self.get_data)
        self.get_data_button.grid(row=1, column=0, pady=5)

        columns = ("period", "number", "sum", "size", "oddEven")
        self.tree = ttk.Treeview(self.frame, columns=columns, show="headings", height=10)
        self.tree.heading("period", text="期号")
        self.tree.heading("number", text="开奖号码")
        self.tree.heading("sum", text="和值")
        self.tree.heading("size", text="大小")
        self.tree.heading("oddEven", text="单双")
        self.tree.column("period", width=80, anchor="center")
        self.tree.column("number", width=100, anchor="center")
        self.tree.column("sum", width=60, anchor="center")
        self.tree.column("size", width=60, anchor="center")
        self.tree.column("oddEven", width=60, anchor="center")
        self.tree.grid(row=2, column=0, pady=5)

        # 预测区域
        self.predict_frame = ttk.Frame(self.frame, padding="10")
        self.predict_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加预测按钮
        self.predict_button = ttk.Button(self.predict_frame, text="预测下一期", command=self.predict_next)
        self.predict_button.grid(row=0, column=0, pady=5)
        
        # 创建预测结果表格
        predict_columns = ("period", "size", "oddEven")
        self.predict_tree = ttk.Treeview(self.predict_frame, columns=predict_columns, show="headings", height=1)
        self.predict_tree.heading("period", text="期号")
        self.predict_tree.heading("size", text="大小")
        self.predict_tree.heading("oddEven", text="单双")
        self.predict_tree.column("period", width=80, anchor="center")
        self.predict_tree.column("size", width=60, anchor="center")
        self.predict_tree.column("oddEven", width=60, anchor="center")
        self.predict_tree.grid(row=1, column=0, pady=5)

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.config(text=message)
        self.update_idletasks()

    def save_prediction(self, period, size_pred, odd_even_pred):
        """保存预测结果"""
        prediction = {
            'period': period,
            'size_pred': size_pred,
            'odd_even_pred': odd_even_pred,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.prediction_history.append(prediction)
        # 保存到文件
        filename = 'prediction_history.json'
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump([LotteryUI.to_serializable(p) for p in self.prediction_history], f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存预测历史时出错: {str(e)}")

    def get_data(self):
        """获取数据的主方法"""
        if self.is_fetching:
            self.update_status("正在获取数据，请稍候...")
            return
            
        self.is_fetching = True
        self.update_status("正在获取数据...")
        print("\n开始获取数据...")

        def fetch_data():
            try:
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        print(f"\n尝试获取数据 (第{retry_count + 1}次)...")
                        
                        # 关闭现有的浏览器实例
                        if hasattr(self.crawler, 'driver') and self.crawler.driver:
                            try:
                                self.crawler.driver.quit()
                            except:
                                pass
                            self.crawler.driver = None
                            self.crawler.is_logged_in = False
                        
                        # 初始化浏览器并登录
                        print("初始化浏览器...")
                        if not self.crawler.login_with_selenium():
                            self.after(0, lambda: self.update_status("登录失败，请重试"))
                            return
                        
                        print("开始获取最新开奖数据...")
                        # 获取数据
                        records = self.crawler.get_latest_ten_happy_k3()
                        if records:
                            print(f"成功获取到 {len(records)} 条记录")
                            self.latest_records = records
                            # 立即更新UI
                            self.after(0, lambda: self._update_ui_immediately(records))
                            self.after(0, lambda: self.update_status("数据获取成功"))
                            # 清理旧数据文件
                            self.crawler.clean_old_data_files()
                            # 验证之前的预测
                            self.verify_prediction(records)
                            break  # 成功获取数据，退出重试循环
                        else:
                            print("未能获取到数据")
                            self.after(0, lambda: self.update_status("未能获取到数据"))
                            break  # 没有数据，退出重试循环
                            
                    except Exception as e:
                        error_msg = f"获取数据时出错: {str(e)}"
                        print(error_msg)
                        retry_count += 1
                        
                        if retry_count < max_retries:
                            print(f"尝试重新获取数据 (第{retry_count + 1}次)...")
                            self.after(0, lambda: self.update_status(f"获取数据失败，正在重试 ({retry_count + 1}/{max_retries})..."))
                            time.sleep(2)  # 等待2秒后重试
                        else:
                            self.after(0, lambda: self.update_status("获取数据失败，请稍后重试"))
                            break
            finally:
                self.is_fetching = False
                print("数据获取过程结束")

        # 在新线程中执行数据获取
        threading.Thread(target=fetch_data, daemon=True).start()

    def _update_ui_immediately(self, records):
        """立即更新UI显示"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加新数据
            for record in records:
                try:
                    values = (
                        record['period'],
                        record['number'],
                        record['sum'],
                        record['size'],
                        record['oddEven']
                    )
                    self.tree.insert("", tk.END, values=values)
                except Exception as e:
                    print(f"插入记录时出错: {str(e)}")
                    print(f"问题记录: {record}")
            
            # 强制更新UI
            self.tree.update_idletasks()
            self.update_idletasks()
        except Exception as e:
            print(f"更新UI时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def verify_prediction(self, records):
        """验证预测结果"""
        if not self.prediction_history:
            return
            
        latest_prediction = self.prediction_history[-1]
        latest_period = latest_prediction['period']
        
        # 查找对应的开奖结果
        for record in records:
            if record['period'] == latest_period:
                # 计算预测准确度
                size_correct = record['size'] == latest_prediction['size_pred']
                odd_even_correct = record['oddEven'] == latest_prediction['odd_even_pred']
                
                # 更新预测历史
                latest_prediction.update({
                    'actual_size': record['size'],
                    'actual_odd_even': record['oddEven'],
                    'size_correct': size_correct,
                    'odd_even_correct': odd_even_correct
                })
                
                # 保存更新后的预测历史
                self.save_prediction(latest_period, latest_prediction['size_pred'], latest_prediction['odd_even_pred'])
                break

    def extract_advanced_features(self, records, window_sizes=[3, 5, 10, 20]):
        """提取高级特征"""
        if not records:
            return []
            
        features = []
        for i in range(len(records)):
            feature = []
            current_record = records[i]
            
            # 1. 基础特征
            numbers = [int(n) for n in current_record['number']]
            feature.extend([
                float(current_record['sum']),  # 和值
                1.0 if current_record['size'] == '大' else 0.0,  # 大小
                1.0 if current_record['oddEven'] == '单' else 0.0,  # 单双
            ])
            
            # 2. 号码特征
            feature.extend(numbers)  # 原始号码
            feature.extend([
                float(max(numbers)),  # 最大值
                float(min(numbers)),  # 最小值
                float(np.std(numbers)),  # 标准差
                float(np.median(numbers)),  # 中位数
                float(len(set(numbers))),  # 不同数字个数
            ])
            
            # 3. 时间窗口特征
            for window_size in window_sizes:
                if i >= window_size:
                    window_records = records[i-window_size:i]
                    
                    # 和值统计
                    sums = [float(r['sum']) for r in window_records]
                    feature.extend([
                        np.mean(sums),  # 平均和值
                        np.std(sums),   # 和值标准差
                        np.max(sums),   # 最大和值
                        np.min(sums),   # 最小和值
                    ])
                    
                    # 大小统计
                    sizes = [1.0 if r['size'] == '大' else 0.0 for r in window_records]
                    feature.extend([
                        np.mean(sizes),  # 大小比例
                        sum(1 for x, y in zip(sizes[:-1], sizes[1:]) if x != y),  # 大小交替次数
                    ])
                    
                    # 单双统计
                    odd_evens = [1.0 if r['oddEven'] == '单' else 0.0 for r in window_records]
                    feature.extend([
                        np.mean(odd_evens),  # 单双比例
                        sum(1 for x, y in zip(odd_evens[:-1], odd_evens[1:]) if x != y),  # 单双交替次数
                    ])
                    
                    # 趋势特征
                    feature.extend([
                        np.polyfit(range(window_size), sums, 1)[0],     # 和值趋势
                        np.polyfit(range(window_size), sizes, 1)[0],    # 大小趋势
                        np.polyfit(range(window_size), odd_evens, 1)[0] # 单双趋势
                    ])
                else:
                    # 填充默认值
                    feature.extend([0.0] * 11)  # 11是每个窗口大小的特征数量
            
            # 4. 连续性特征
            if i > 0:
                prev_record = records[i-1]
                feature.extend([
                    1.0 if current_record['size'] == prev_record['size'] else 0.0,
                    1.0 if current_record['oddEven'] == prev_record['oddEven'] else 0.0,
                ])
            else:
                feature.extend([0.0, 0.0])
            
            features.append(feature)
            
        return np.array(features)
    
    def train_and_evaluate_models(self, X, y_size, y_odd_even):
        """训练和评估模型"""
        # 使用时间序列分割
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 初始化模型
        size_model = XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            use_label_encoder=False,
            eval_metric='logloss'
        )
        
        odd_even_model = XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            use_label_encoder=False,
            eval_metric='logloss'
        )
        
        # 训练和评估
        size_scores = []
        odd_even_scores = []
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X[train_idx], X[val_idx]
            y_size_train, y_size_val = y_size[train_idx], y_size[val_idx]
            y_odd_even_train, y_odd_even_val = y_odd_even[train_idx], y_odd_even[val_idx]

            # 检查标签是否只有一种类别，若是则跳过本次分割
            if len(np.unique(y_size_train)) < 2 or len(np.unique(y_size_val)) < 2:
                continue
            if len(np.unique(y_odd_even_train)) < 2 or len(np.unique(y_odd_even_val)) < 2:
                continue

            # 训练大小模型
            size_model.fit(X_train, y_size_train, eval_set=[(X_val, y_size_val)], verbose=False)
            # 训练单双模型
            odd_even_model.fit(X_train, y_odd_even_train, eval_set=[(X_val, y_odd_even_val)], verbose=False)
            
            # 评估
            size_pred = size_model.predict(X_val)
            odd_even_pred = odd_even_model.predict(X_val)
            
            size_scores.append({
                'accuracy': accuracy_score(y_size_val, size_pred),
                'precision': precision_score(y_size_val, size_pred),
                'recall': recall_score(y_size_val, size_pred),
                'f1': f1_score(y_size_val, size_pred)
            })
            
            odd_even_scores.append({
                'accuracy': accuracy_score(y_odd_even_val, odd_even_pred),
                'precision': precision_score(y_odd_even_val, odd_even_pred),
                'recall': recall_score(y_odd_even_val, odd_even_pred),
                'f1': f1_score(y_odd_even_val, odd_even_pred)
            })
        
        return size_model, odd_even_model, size_scores, odd_even_scores
    
    def predict_next(self):
        """优化版预测（重点改进大小预测，自动清理旧数据）"""
        if not self.latest_records:
            self.update_status("请先获取数据")
            return
            
        def do_predict():
            try:
                self.update_status("正在预测...")
                window = 100
                records = self.latest_records[:window]
                
                if len(records) < 30:
                    self.update_status("数据不足，请先获取更多数据")
                    return
                
                # 增强版特征工程（重点优化大小预测）
                def enhanced_features(records):
                    features = []
                    for i, r in enumerate(records):
                        nums = [int(x) for x in r['number']]
                        sum_val = int(r['sum'])
                        
                        # 1. 基础特征
                        span = max(nums) - min(nums)
                        is_serial = 1 if (max(nums)-min(nums)==2 and len(set(nums))==3) else 0
                        
                        # 2. 大小专项特征
                        last20 = records[i+1:i+21] if i+21<=len(records) else records[i+1:]
                        big_ratio = sum(1 for x in last20 if x['size']=='大')/len(last20) if last20 else 0.5
                        sum_trend = np.polyfit(range(len(last20)), [int(x['sum']) for x in last20], 1)[0] if last20 else 0
                        
                        # 3. 和值分布特征
                        sum_std = np.std([int(x['sum']) for x in last20]) if last20 else 0
                        sum_diff = sum_val - int(records[i-1]['sum']) if i>0 else 0
                        
                        features.append([
                            sum_val, span, is_serial,
                            big_ratio, sum_trend, sum_std, sum_diff
                        ])
                    return np.array(features)
                
                X, y_size, y_odd_even = self.build_features_and_labels(records)
                X = np.hstack([X, enhanced_features(records)])
                X_train, X_pred = X[:-1], X[-1:]
                y_size_train = y_size[:-1]
                
                # 重点优化大小模型
                size_model = CatBoostClassifier(
                    iterations=200,
                    depth=6,
                    learning_rate=0.05,
                    loss_function='Logloss',
                    verbose=0,
                    random_state=42
                )
                size_model.fit(X_train, y_size_train)
                
                # 单双预测保持原样
                odd_even_model = lgb.LGBMClassifier(n_estimators=150)
                odd_even_model.fit(X_train, y_odd_even[:-1])
                
                # 预测
                size_pred = '大' if size_model.predict(X_pred)[0] == 1 else '小'
                odd_even_pred = '单' if odd_even_model.predict(X_pred)[0] == 1 else '双'
                
                # 显示结果
                next_period = str(int(records[0]['period']) + 1)
                self.predict_tree.delete(*self.predict_tree.get_children())
                self.predict_tree.insert("", tk.END, values=(next_period, size_pred, odd_even_pred))
                self.update_status("预测完成")
                
                # 自动清理旧数据文件（保留最近5个）
                self.clean_data_files(max_files=5)
                
            except Exception as e:
                # 异常处理（保持不变）
                pass

        threading.Thread(target=do_predict, daemon=True).start()

    def analyze_size_pattern(self, records, window_size=5):
        recent_records = records[:window_size]
        size_sequence = [1 if r['size'] == '大' else 0 for r in recent_records]
        current_streak = 1
        for i in range(1, len(size_sequence)):
            if size_sequence[i] == size_sequence[0]:
                current_streak += 1
            else:
                break
        
        # 计算最近几期的大小比例
        size_ratio = sum(size_sequence) / len(size_sequence)
        
        # 计算和值趋势
        sums = [float(r['sum']) for r in recent_records]
        sum_trend = np.polyfit(range(len(sums)), sums, 1)[0]
        
        return {
            'current_streak': current_streak,
            'size_ratio': size_ratio,
            'sum_trend': sum_trend,
            'last_size': records[0]['size']
        }

    def build_features_and_labels(self, records, window_sizes=[5, 10, 20]):
        """
        输入：历史开奖列表（records）
        输出：特征矩阵X，标签数组y_size, y_odd_even
        """
        features = []
        y_size = []
        y_odd_even = []
        for i in range(len(records)):
            feature = []
            current_record = records[i]
            # 基础特征
            numbers = [int(n) for n in current_record['number']]
            feature.extend([
                float(current_record['sum']),
                1.0 if current_record['size'] == '大' else 0.0,
                1.0 if current_record['oddEven'] == '单' else 0.0,
                float(max(numbers)),
                float(min(numbers)),
                float(np.std(numbers)),
                float(np.median(numbers)),
                float(len(set(numbers)))
            ])
            feature.extend(numbers)
            # 滞后特征（前1~3期的和值、大/小、单双）
            for lag in [1, 2, 3]:
                if i >= lag:
                    prev = records[i-lag]
                    feature.append(float(prev['sum']))
                    feature.append(1.0 if prev['size'] == '大' else 0.0)
                    feature.append(1.0 if prev['oddEven'] == '单' else 0.0)
                else:
                    feature.extend([0.0, 0.0, 0.0])
            # 统计特征（滑动窗口）
            for window in window_sizes:
                if i >= window:
                    window_records = records[i-window:i]
                    sums = [float(r['sum']) for r in window_records]
                    sizes = [1.0 if r['size'] == '大' else 0.0 for r in window_records]
                    odd_evens = [1.0 if r['oddEven'] == '单' else 0.0 for r in window_records]
                    feature.extend([
                        np.mean(sums), np.std(sums), np.max(sums), np.min(sums),
                        np.mean(sizes), np.std(sizes),
                        np.mean(odd_evens), np.std(odd_evens)
                    ])
                    # 趋势特征
                    feature.append(np.polyfit(range(window), sums, 1)[0])
                    feature.append(np.polyfit(range(window), sizes, 1)[0])
                    feature.append(np.polyfit(range(window), odd_evens, 1)[0])
                else:
                    feature.extend([0.0]*11)
            # 连续性特征
            if i > 0:
                prev = records[i-1]
                feature.append(1.0 if current_record['size'] == prev['size'] else 0.0)
                feature.append(1.0 if current_record['oddEven'] == prev['oddEven'] else 0.0)
            else:
                feature.extend([0.0, 0.0])
            features.append(feature)
            y_size.append(1 if current_record['size'] == '大' else 0)
            y_odd_even.append(1 if current_record['oddEven'] == '单' else 0)
        X = np.array(features)
        y_size = np.array(y_size)
        y_odd_even = np.array(y_odd_even)
        return X, y_size, y_odd_even

    def clean_data_files(self, max_files=5):
        """自动清理旧数据文件，只保留最近的max_files个"""
        try:
            data_files = []
            for f in os.listdir('.'):
                if f.startswith('happy_k3_latest_') and f.endswith('.json'):
                    data_files.append((f, os.path.getmtime(f)))
            
            # 按修改时间排序
            data_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除多余文件
            for f, _ in data_files[max_files:]:
                try:
                    os.remove(f)
                    print(f"已删除旧数据文件: {f}")
                except Exception as e:
                    print(f"删除文件失败: {f} - {str(e)}")
                    
        except Exception as e:
            print(f"清理文件时出错: {str(e)}")

class LotteryCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://m.dpdqhf.com:6524"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://m.dpdqhf.com:6524',
            'Referer': 'https://m.dpdqhf.com:6524/lottery/K3/SG1K3',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        self.session.headers.update(self.headers)
        # 禁用SSL验证
        self.session.verify = False
        self.is_logged_in = False
        self.login_lock = threading.Lock()

    def login_with_selenium(self):
        with self.login_lock:
            if self.is_logged_in and hasattr(self, 'driver') and self.driver:
                try:
                    # 测试driver是否仍然有效
                    self.driver.current_url
                    print("已登录，复用现有driver。")
                    return self.driver
                except:
                    print("现有driver已失效，重新初始化...")
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                    self.is_logged_in = False

            print("使用Selenium进行登录...")
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=IsolateOrigins,site-per-process')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            chrome_options.add_argument('--disable-gpu-sandbox')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    print(f"尝试登录 (第{retry_count + 1}次)...")
                    print("初始化Chrome浏览器...")
                    
                    # 使用webdriver_manager自动下载和管理ChromeDriver
                    print("使用webdriver_manager自动下载和管理ChromeDriver...")
                    # 设置ChromeDriver服务
                    service = Service(ChromeDriverManager().install())
                    
                    # 初始化Chrome WebDriver
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                    print("Chrome WebDriver初始化成功")
                    
                    # 设置页面加载超时
                    driver.set_page_load_timeout(60)
                    driver.set_script_timeout(60)
                    
                    print("访问登录页面...")
                    driver.get(f"{self.base_url}/login")
                    
                    # 等待页面加载完成
                    print("等待页面加载...")
                    wait = WebDriverWait(driver, 20)
                    wait.until(EC.presence_of_element_located((By.ID, "app")))
                    
                    # 等待登录框出现
                    print("等待登录框出现...")
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']")))
                    
                    # 重新获取元素
                    username_input = driver.find_element(By.CSS_SELECTOR, "input[type='text']")
                    password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                    
                    print("输入用户名和密码...")
                    username_input.clear()
                    username_input.send_keys("zhu5678")
                    password_input.clear()
                    password_input.send_keys("Zhucom5678")
                    
                    print("查找并点击登录按钮...")
                    # 使用JavaScript点击登录按钮
                    login_button = driver.find_element(By.XPATH, "//div[contains(@class, 'loginBtn')]/a[contains(text(), '立即登录')]")
                    driver.execute_script("arguments[0].click();", login_button)
                    
                    print("等待登录成功跳转...")
                    wait.until(EC.url_contains("home"))
                    print(f"登录后页面URL: {driver.current_url}")
                    
                    # 验证登录状态
                    if "login" in driver.current_url.lower():
                        print("登录可能未成功，尝试重新登录...")
                        retry_count += 1
                        if retry_count < max_retries:
                            driver.quit()
                            time.sleep(5)  # 等待5秒后重试
                            continue
                        else:
                            print("达到最大重试次数，登录失败")
                            return None
                    
                    # 处理弹窗
                    print("处理弹窗...")
                    try:
                        popup = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#app > div > div.hide.stopMove > div:nth-child(5) > div.wrapBox > div.head > p")))
                        popup.click()
                    except:
                        print("未找到弹窗或弹窗已关闭")
                    
                    # 获取所有cookie
                    print("\n获取Cookies...")
                    cookies = driver.get_cookies()
                    print("\n获取到的Cookies:")
                    for cookie in cookies:
                        print(f"{cookie['name']}: {cookie['value']}")
                        self.session.cookies.set(cookie['name'], cookie['value'])
                    
                    self.is_logged_in = True
                    self.driver = driver
                    return driver
                    
                except Exception as e:
                    print(f"登录过程出错: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        try:
                            if 'driver' in locals():
                                driver.quit()
                        except:
                            pass
                        print(f"等待5秒后重试...")
                        time.sleep(5)
                        continue
                    else:
                        print("达到最大重试次数，登录失败")
                        return None

    def start_refresh_thread(self):
        """启动一个线程，每10秒点击一次刷新按钮"""
        # 删除刷新线程的启动代码
        pass

    def get_lottery_data(self, save_to_file=True):
        driver = self.login_with_selenium()
        if not driver:
            return None
            
        print("\n开始获取开奖数据...")
        try:
            # 访问开奖页面
            print("访问开奖页面...")
            driver.get(f"{self.base_url}/lottery/K3/SG3K3")
            time.sleep(15)  # 等待页面加载
            
            # 打印当前URL
            print(f"\n当前URL: {driver.current_url}")
            
            # 刷新页面以确保会话有效
            print("刷新页面以确保会话有效...")
            driver.refresh()
            time.sleep(10)  # 等待页面重新加载
            
            # 获取所有cookie
            cookies = driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])
            
            # 使用API获取开奖数据
            print("\n请求开奖数据API...")
            api_url = "https://2b0csw21.ruexjigbgpwl.com/vap/api/im/friendShip/listGet/history"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Origin': 'https://m.dpdqhf.com:6524',
                'Referer': 'https://m.dpdqhf.com:6524/lottery/K3/SG3K3',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
            
            # 添加所有cookie到headers
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            headers['Cookie'] = cookie_str
            
            all_records = []
            page = 1
            page_size = 100  # 每页获取100条数据
            
            while True:
                print(f"\n正在获取第 {page} 页数据...")
                # 准备请求数据
                data = {
                    "page": page,
                    "size": page_size,
                    "type": "K3",
                    "lotteryType": "SG3K3"
                }
                
                try:
                    response = self.session.post(api_url, headers=headers, json=data)
                    print(f"API状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            response_data = response.json()
                            if 'data' in response_data and 'list' in response_data['data']:
                                records = response_data['data']['list']
                                if not records:  # 如果没有更多数据，退出循环
                                    break
                                    
                                all_records.extend(records)
                                print(f"当前页获取到 {len(records)} 条记录")
                                
                                # 显示当前页的前5条记录
                                print("\n当前页前5条记录:")
                                for record in records[:5]:
                                    print(f"期号: {record.get('period', '')} | "
                                          f"开奖号码: {record.get('number', '')} | "
                                          f"大小: {record.get('size', '')} | "
                                          f"单双: {record.get('oddEven', '')}")
                                
                                page += 1
                                time.sleep(2)  # 避免请求过快
                            else:
                                print("API返回数据格式不正确")
                                print(f"API响应: {response.text[:500]}")
                                break
                        except Exception as e:
                            print(f"解析JSON数据时出错: {str(e)}")
                            break
                    else:
                        print("API请求失败")
                        print(f"API响应: {response.text[:500]}")
                        break
                        
                except Exception as e:
                    print(f"获取数据时出错：{str(e)}")
                    break
            
            # 保存数据到文件
            if save_to_file and all_records:
                filename = f"lottery_data_{time.strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(all_records, f, ensure_ascii=False, indent=2)
                print(f"\n数据已保存到文件: {filename}")
            
            print(f"\n总共获取到 {len(all_records)} 条开奖记录")
            return True
            
        except Exception as e:
            print(f"程序出错：{str(e)}")
            return False

    def get_latest_ten_records(self, save_to_file=True):
        driver = self.login_with_selenium()
        if not driver:
            return None
            
        print("\n开始获取最新十期开奖数据...")
        try:
            # 访问开奖页面
            print("访问开奖页面...")
            driver.get(f"{self.base_url}/lottery/K3/SG3K3")
            time.sleep(15)  # 等待页面加载
            
            # 打印当前URL
            print(f"\n当前URL: {driver.current_url}")
            
            # 刷新页面以确保会话有效
            print("刷新页面以确保会话有效...")
            driver.refresh()
            time.sleep(10)  # 等待页面重新加载
            
            # 获取所有cookie
            cookies = driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])
            
            # 使用API获取开奖数据
            print("\n请求开奖数据API...")
            api_url = "https://2b0csw21.ruexjigbgpwl.com/vap/api/im/friendShip/listGet/history"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Origin': 'https://m.dpdqhf.com:6524',
                'Referer': 'https://m.dpdqhf.com:6524/lottery/K3/SG3K3',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
            
            # 添加所有cookie到headers
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            headers['Cookie'] = cookie_str
            
            # 准备请求数据
            data = {
                "page": 1,
                "size": 10,  # 只获取10条数据
                "type": "K3",
                "lotteryType": "SG3K3"
            }
            
            try:
                response = self.session.post(api_url, headers=headers, json=data)
                print(f"API状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        if 'data' in response_data and 'list' in response_data['data']:
                            records = response_data['data']['list']
                            
                            # 显示获取到的记录
                            print("\n最新十期开奖记录:")
                            for record in records:
                                print(f"期号: {record.get('period', '')} | "
                                      f"开奖号码: {record.get('number', '')} | "
                                      f"大小: {record.get('size', '')} | "
                                      f"单双: {record.get('oddEven', '')}")
                            
                            # 保存数据到文件
                            if save_to_file and records:
                                filename = f"latest_ten_records_{time.strftime('%Y%m%d_%H%M%S')}.json"
                                with open(filename, 'w', encoding='utf-8') as f:
                                    json.dump(records, f, ensure_ascii=False, indent=2)
                                print(f"\n数据已保存到文件: {filename}")
                            
                            return records
                        else:
                            print("API返回数据格式不正确")
                            print(f"API响应: {response.text[:500]}")
                            return None
                    except Exception as e:
                        print(f"解析JSON数据时出错: {str(e)}")
                        return None
                else:
                    print("API请求失败")
                    print(f"API响应: {response.text[:500]}")
                    return None
                    
            except Exception as e:
                print(f"获取数据时出错：{str(e)}")
                return None
            
        except Exception as e:
            print(f"程序出错：{str(e)}")
            return None

    def get_latest_ten_happy_k3(self, save_to_file=True):
        """获取欢乐三分快3最新开奖数据"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                driver = self.login_with_selenium()
                if not driver:
                    print("登录失败，无法获取数据。")
                    return None

                print("\n开始获取欢乐三分快3最新开奖数据...")
                try:
                    # 访问开奖页面
                    print("访问开奖页面...")
                    driver.set_page_load_timeout(60)
                    driver.get(f"{self.base_url}/lottery/K3/SG3K3")
                    time.sleep(10)

                    # 等待页面加载完成
                    wait = WebDriverWait(driver, 15)
                    wait.until(EC.presence_of_element_located((By.CLASS_NAME, "LotteryPastOpenList")))
                    
                    # 获取页面内容
                    page_source = driver.page_source
                    soup = BeautifulSoup(page_source, 'html.parser')
                    
                    # 查找开奖列表
                    lottery_list = soup.find('div', class_='LotteryPastOpenList')
                    if not lottery_list:
                        print("未找到开奖列表")
                        return None
                        
                    # 获取所有开奖记录
                    records = []
                    list_cols = lottery_list.find_all('div', class_='list-col')
                    
                    for col in list_cols[:50]:  # 关键修改点
                        try:
                            # 获取期号
                            period = col.find('div', class_='van-li-first').text.strip()
                            
                            # 获取开奖号码
                            number_div = col.find('div', class_='openPastDiceText')
                            numbers = [span.text.strip() for span in number_div.find_all('span', class_='Dice')]
                            number = ''.join(numbers)
                            
                            # 获取和值
                            sum_value = col.find_all('div', class_='df-center')[0].text.strip()
                            
                            # 获取大小
                            size = col.find_all('div', class_='df-center')[1].find('span').text.strip()
                            
                            # 获取单双
                            odd_even = col.find_all('div', class_='df-center')[2].find('span').text.strip()
                            
                            record = {
                                'period': period,
                                'number': number,
                                'sum': sum_value,
                                'size': size,
                                'oddEven': odd_even
                            }
                            records.append(record)
                            
                        except Exception as e:
                            print(f"解析记录时出错: {str(e)}")
                            continue
                    
                    # 显示获取到的记录
                    print(f"\n成功获取到 {len(records)} 条欢乐三分快3开奖记录")
                    for record in records[:5]:  # 只显示前5条
                        print(f"期号: {record['period']} | 号码: {record['number']} | 和值: {record['sum']}")
                    
                    # 保存数据到文件
                    if save_to_file and records:
                        filename = f"happy_k3_latest_{time.strftime('%Y%m%d_%H%M%S')}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(records, f, ensure_ascii=False, indent=2)
                        print(f"数据已保存到文件: {filename}")
                    
                    return records
                    
                except Exception as e:
                    print(f"获取数据时出错：{str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"尝试重新获取数据 (第{retry_count + 1}次)...")
                        time.sleep(5)
                        continue
                    return None
                    
            except Exception as e:
                print(f"程序出错：{str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"尝试重新初始化 (第{retry_count + 1}次)...")
                    time.sleep(5)
                    continue
                return None

    def get_article_content(self, url):
        try:
            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找文章内容区域
            content_div = soup.find('div', class_='article-content')
            if not content_div:
                print("未找到文章内容区域")
                return None
                
            # 获取文章标题
            title = soup.find('h1', class_='article-title')
            title_text = title.text.strip() if title else "无标题"
            
            # 获取文章正文
            paragraphs = content_div.find_all('p')
            content = '\n'.join([p.text.strip() for p in paragraphs])
            
            return {
                'title': title_text,
                'content': content
            }
            
        except Exception as e:
            print(f"获取文章内容时出错：{str(e)}")
            return None

    def clean_old_data_files(self):
        """清理旧的数据文件，只保留最新的两个文件"""
        try:
            # 获取所有happy_k3_latest_ten开头的json文件
            data_files = []
            for file in os.listdir('.'):
                if file.startswith('happy_k3_latest_ten_') and file.endswith('.json'):
                    # 获取文件的完整路径
                    file_path = os.path.join('.', file)
                    # 获取文件的修改时间
                    mtime = os.path.getmtime(file_path)
                    data_files.append((file_path, mtime))
            
            # 按修改时间排序，最新的在前面
            data_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除除了最新的两个文件之外的所有文件
            for file_path, _ in data_files[2:]:
                try:
                    os.remove(file_path)
                    print(f"已删除旧数据文件: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"删除文件 {os.path.basename(file_path)} 时出错: {str(e)}")
            
            # 打印保留的文件
            if data_files:
                print("\n保留的数据文件:")
                for file_path, mtime in data_files[:2]:
                    print(f"- {os.path.basename(file_path)} (修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))})")
            
        except Exception as e:
            print(f"清理旧数据文件时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

if __name__ == "__main__":
    try:
        print("程序启动...")
        print("当前工作目录:", os.getcwd())
        print("检查chromedriver.exe是否存在...")
        chromedriver_path = os.path.join(os.getcwd(), "chromedriver.exe")
        if os.path.exists(chromedriver_path):
            print(f"找到chromedriver.exe: {chromedriver_path}")
        else:
            print(f"未找到chromedriver.exe: {chromedriver_path}")
        
        # 删除上一次缓存的数据文件
        cache_file = "happy_k3_latest_ten_20250518_114727.json"
        if os.path.exists(cache_file):
            print(f"删除缓存文件: {cache_file}")
            os.remove(cache_file)
        
        print("初始化主窗口...")
        app = LotteryUI()
        
        # 添加窗口关闭事件处理
        def on_closing():
            print("\n正在关闭程序...")
            try:
                # 关闭浏览器驱动
                if hasattr(app, 'crawler') and hasattr(app.crawler, 'driver'):
                    print("关闭浏览器驱动...")
                    app.crawler.driver.quit()
                # 销毁主窗口
                print("销毁主窗口...")
                app.destroy()
            except Exception as e:
                print(f"关闭程序时出错: {str(e)}")
            finally:
                print("程序已关闭")
                os._exit(0)  # 强制退出程序
        
        # 绑定窗口关闭事件
        app.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("启动主循环...")
        app.mainloop()
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        import traceback
        print("错误堆栈:")
        print(traceback.format_exc())
    finally:
        print("程序退出") 