import json
import os
from datetime import datetime
from crawler import LotteryCrawler
from models.lightgbm_predictor import LightGBMPredictor

def analyze_lottery_data():
    # 创建爬虫实例
    crawler = LotteryCrawler()
    
    # 获取数据
    print("正在获取彩票数据...")
    all_records = []
    page = 1
    page_size = 100  # 每页获取100条数据
    
    while len(all_records) < 100:
        print(f"正在获取第 {page} 页数据...")
        # 使用get_latest_ten_happy_k3方法获取数据
        records = crawler.get_latest_ten_happy_k3(save_to_file=False)
        if not records:
            print("未能获取到数据")
            break
        all_records.extend(records)
        page += 1
        if len(all_records) >= 100:
            all_records = all_records[:100]  # 只保留前100条数据
            break
    
    if not all_records:
        print("未能获取到数据")
        return
    
    # 确保获取到100条数据
    if len(all_records) < 100:
        print(f"警告：只获取到{len(all_records)}条数据，少于预期的100条")
    
    # 分析结果
    results = []
    results.append("彩票数据分析报告")
    results.append("=" * 50)
    results.append(f"分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    results.append(f"分析数据量：{len(all_records)}条")
    results.append("-" * 50)
    
    # 1. 分析大小占比
    big_count = sum(1 for r in all_records if float(r['sum']) > 10)
    small_count = len(all_records) - big_count
    big_ratio = big_count / len(all_records) * 100
    small_ratio = small_count / len(all_records) * 100
    
    results.append("\n1. 大小占比分析")
    results.append(f"和值大于10的占比：{big_ratio:.2f}%")
    results.append(f"和值小于等于10的占比：{small_ratio:.2f}%")
    
    # 2. 分析连续大小出现次数
    results.append("\n2. 连续大小出现次数分析")
    
    def analyze_consecutive_occurrences(records, condition_func, name):
        consecutive_counts = {i: 0 for i in range(1, 9)}  # 记录1-8次连续出现的次数
        current_streak = 0
        
        for i in range(len(records)):
            if condition_func(records[i]):
                current_streak += 1
            else:
                if current_streak > 0:
                    if current_streak <= 8:
                        consecutive_counts[current_streak] += 1
                current_streak = 0
        
        # 处理最后一组连续
        if current_streak > 0 and current_streak <= 8:
            consecutive_counts[current_streak] += 1
        
        # 计算总次数
        total_occurrences = sum(consecutive_counts.values())
        
        # 输出结果
        results.append(f"\n{name}连续出现次数分析：")
        for count, occurrences in consecutive_counts.items():
            if total_occurrences > 0:
                ratio = occurrences / total_occurrences * 100
                results.append(f"连续出现{count}次的占比：{ratio:.2f}%")
            else:
                results.append(f"连续出现{count}次的占比：0.00%")
    
    # 分析连续大号
    analyze_consecutive_occurrences(
        all_records,
        lambda r: float(r['sum']) > 10,
        "大号"
    )
    
    # 分析连续小号
    analyze_consecutive_occurrences(
        all_records,
        lambda r: float(r['sum']) <= 10,
        "小号"
    )
    
    # 3. 分析连续单双出现次数
    results.append("\n3. 连续单双出现次数分析")
    
    # 分析连续单号
    analyze_consecutive_occurrences(
        all_records,
        lambda r: r['oddEven'] == '单',
        "单号"
    )
    
    # 分析连续双号
    analyze_consecutive_occurrences(
        all_records,
        lambda r: r['oddEven'] == '双',
        "双号"
    )
    
    # 4. 分析和值连续出现次数
    results.append("\n4. 和值连续出现次数分析")
    
    # 分析和值大于10的连续出现
    analyze_consecutive_occurrences(
        all_records,
        lambda r: float(r['sum']) > 10,
        "和值大于10"
    )
    
    # 分析和值小于等于10的连续出现
    analyze_consecutive_occurrences(
        all_records,
        lambda r: float(r['sum']) <= 10,
        "和值小于等于10"
    )
    
    # 5. LightGBM模型预测
    results.append("\n5. LightGBM模型预测分析")
    try:
        # 创建预测器实例
        predictor = LightGBMPredictor()
        
        # 训练模型
        print("正在训练LightGBM模型...")
        predictor.train(all_records)
        
        # 预测下一期
        prediction = predictor.predict(all_records)
        
        results.append(f"预测下一期和值：{prediction['predicted_sum']:.2f}")
        results.append(f"预测大小：{'大' if prediction['is_big'] else '小'}")
        results.append(f"预测单双：{'单' if prediction['is_odd'] else '双'}")
        
        # 保存模型
        predictor.save_model()
        results.append("模型已保存")
        
    except Exception as e:
        results.append(f"LightGBM预测出错：{str(e)}")
    
    # 保存分析结果到固定的测试报告文件
    output_filename = "lottery_analysis_report.txt"
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(results))
    
    print(f"\n分析完成，结果已保存到文件：{output_filename}")
    
    # 打印分析结果
    print("\n分析结果：")
    print('\n'.join(results))

if __name__ == "__main__":
    try:
        analyze_lottery_data()
    except Exception as e:
        print(f"程序运行出错：{str(e)}")
        import traceback
        print(traceback.format_exc()) 