import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QPushButton, QTableWidget, 
                           QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor
from crawler import LotteryCrawler

class LotteryGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.crawler = LotteryCrawler()
        self.initUI()
        
    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle('欢乐三分快3开奖数据')
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title = QLabel('欢乐三分快3最新开奖数据')
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont('Arial', 16, QFont.Bold))
        layout.addWidget(title)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['期号', '开奖号码', '和值', '大小', '单双'])
        
        # 设置表格样式
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setAlternatingRowColors(True)
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f0f0f0;
                gridline-color: #d0d0d0;
            }
            QHeaderView::section {
                background-color: #4a86e8;
                color: white;
                padding: 5px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.table)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建刷新按钮
        refresh_btn = QPushButton('刷新数据')
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
        """)
        refresh_btn.clicked.connect(self.update_data)
        button_layout.addWidget(refresh_btn)
        
        layout.addLayout(button_layout)
        
        # 创建自动刷新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(60000)  # 每60秒刷新一次
        
        # 初始化数据
        self.update_data()
        
    def update_data(self):
        try:
            # 获取最新数据
            records = self.crawler.get_latest_ten_happy_k3(save_to_file=False)
            if not records:
                return
                
            # 清空表格
            self.table.setRowCount(0)
            
            # 添加数据到表格
            for record in records:
                row = self.table.rowCount()
                self.table.insertRow(row)
                
                # 设置期号
                period_item = QTableWidgetItem(record['period'])
                period_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 0, period_item)
                
                # 设置开奖号码
                number_item = QTableWidgetItem(record['number'])
                number_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 1, number_item)
                
                # 设置和值
                sum_item = QTableWidgetItem(record['sum'])
                sum_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 2, sum_item)
                
                # 设置大小
                size_item = QTableWidgetItem(record['size'])
                size_item.setTextAlignment(Qt.AlignCenter)
                if record['size'] == '大':
                    size_item.setForeground(QColor('red'))
                else:
                    size_item.setForeground(QColor('blue'))
                self.table.setItem(row, 3, size_item)
                
                # 设置单双
                odd_even_item = QTableWidgetItem(record['oddEven'])
                odd_even_item.setTextAlignment(Qt.AlignCenter)
                if record['oddEven'] == '双':
                    odd_even_item.setForeground(QColor('red'))
                else:
                    odd_even_item.setForeground(QColor('blue'))
                self.table.setItem(row, 4, odd_even_item)
                
        except Exception as e:
            print(f"更新数据时出错：{str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格
    gui = LotteryGUI()
    gui.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 